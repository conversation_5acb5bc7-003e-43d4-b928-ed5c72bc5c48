@echo off
echo ========================================
echo    Arduino MEGA 2560 一键烧录工具
echo ========================================
echo.
echo 正在编译程序...
platformio run
if %errorlevel% neq 0 (
    echo.
    echo ❌ 编译失败！请检查代码错误。
    pause
    exit /b 1
)

echo.
echo ✅ 编译成功！
echo.
echo 正在烧录到Arduino MEGA 2560...
platformio run --target upload --upload-port COM15
if %errorlevel% neq 0 (
    echo.
    echo ❌ 烧录失败！请检查Arduino连接。
    echo 提示：确保Arduino通过USB连接到COM15端口
    pause
    exit /b 1
)

echo.
echo 🎉 烧录成功！
echo 您的Arduino MEGA 2560现在正在运行新程序。
echo.
pause
