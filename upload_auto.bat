@echo off
echo ========================================
echo    Arduino MEGA 2560 智能烧录工具
echo ========================================
echo.
echo 正在编译程序...
platformio run
if %errorlevel% neq 0 (
    echo.
    echo ❌ 编译失败！请检查代码错误。
    pause
    exit /b 1
)

echo.
echo ✅ 编译成功！
echo.
echo 正在检测Arduino设备...
platformio device list | findstr "Arduino Mega 2560"
if %errorlevel% neq 0 (
    echo.
    echo ⚠️  未检测到Arduino MEGA 2560设备
    echo 请确保：
    echo 1. Arduino通过USB连接到电脑
    echo 2. 驱动程序已正确安装
    echo.
    pause
    exit /b 1
)

echo.
echo 正在自动烧录...
platformio run --target upload
if %errorlevel% neq 0 (
    echo.
    echo ❌ 烧录失败！
    pause
    exit /b 1
)

echo.
echo 🎉 烧录成功！
echo.
pause
