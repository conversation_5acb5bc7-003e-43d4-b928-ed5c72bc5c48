#include <Arduino.h>
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>

#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
#define OLED_RESET -1
#define SCREEN_ADDRESS 0x3C

Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET);

unsigned long currentTime = 0;
unsigned long lastUpdate = 0;

void setup()
{
  Serial.begin(9600);

  if (!display.begin(SSD1306_SWITCHCAPVCC, SCREEN_ADDRESS))
  {
    Serial.println("OLED init failed");
    for (;;)
      ;
  }

  display.clearDisplay();
  display.setTextSize(2);
  display.setTextColor(SSD1306_WHITE);
  display.setCursor(20, 25);
  display.print("CLOCK");
  display.display();
  delay(1000);

  currentTime = 0;
  lastUpdate = millis();
}

void loop()
{
  if (millis() - lastUpdate >= 1000)
  {
    currentTime++;
    lastUpdate = millis();

    int hours = (currentTime / 3600) % 24;
    int minutes = (currentTime / 60) % 60;
    int seconds = currentTime % 60;

    display.clearDisplay();

    // 主时钟显示 - 居中且简洁
    display.setTextSize(3);
    display.setCursor(4, 22);
    if (hours < 10)
      display.print("0");
    display.print(hours);
    display.print(":");
    if (minutes < 10)
      display.print("0");
    display.print(minutes);
    display.print(":");
    if (seconds < 10)
      display.print("0");
    display.print(seconds);

    // 顶部标题 - 简洁英文
    display.setTextSize(1);
    display.setCursor(35, 5);
    display.print("ARDUINO");

    // 底部运行时间 - 简洁显示
    display.setCursor(25, 55);
    display.print("Runtime:");
    display.print(currentTime);
    display.print("s");

    display.display();
  }
}
