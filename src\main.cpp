#include <Arduino.h>
#include <Wire.h>
#include <Adafruit_GFX.h>
#include <Adafruit_SSD1306.h>

#define SCREEN_WIDTH 128    // OLED显示宽度(像素)
#define SCREEN_HEIGHT 64    // OLED显示高度(像素)
#define OLED_RESET -1       // 重置引脚(-1表示共享Arduino重置引脚)
#define SCREEN_ADDRESS 0x3C // SSD1306的I2C地址(通常是0x3C或0x3D)

Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET); // 创建显示对象

unsigned long currentTime = 0; // 当前时间(秒)
unsigned long lastUpdate = 0;  // 上次更新时间

void setup()
{
  Serial.begin(9600); // 初始化串口通信

  // 初始化SSD1306显示屏
  if (!display.begin(SSD1306_SWITCHCAPVCC, SCREEN_ADDRESS))
  {
    Serial.println(F("SSD1306分配失败"));
    for (;;)
      ; // 无限循环,停止程序
  }

  display.clearDisplay();                // 清除显示缓冲区
  display.setTextSize(1);                // 设置文本大小
  display.setTextColor(SSD1306_WHITE);   // 设置文本颜色
  display.setCursor(0, 0);               // 设置光标位置
  display.println(F("时钟初始化中...")); // 显示初始化信息
  display.display();                     // 更新显示
  delay(2000);                           // 等待2秒

  // 设置初始时间(可以根据需要调整)
  currentTime = 0; // 从00:00:00开始
  lastUpdate = millis();
}

void loop()
{
  // 每秒更新一次时间
  if (millis() - lastUpdate >= 1000)
  {
    currentTime++;
    lastUpdate = millis();

    // 计算小时、分钟、秒
    int hours = (currentTime / 3600) % 24;
    int minutes = (currentTime / 60) % 60;
    int seconds = currentTime % 60;

    // 清除显示
    display.clearDisplay();

    // 显示大时钟
    display.setTextSize(3); // 大字体
    display.setCursor(10, 20);
    if (hours < 10)
      display.print("0");
    display.print(hours);
    display.print(":");
    if (minutes < 10)
      display.print("0");
    display.print(minutes);
    display.print(":");
    if (seconds < 10)
      display.print("0");
    display.print(seconds);

    // 显示日期信息(示例)
    display.setTextSize(1);
    display.setCursor(25, 5);
    display.print("Arduino时钟");

    display.setCursor(15, 55);
    display.print("运行时间: ");
    display.print(currentTime);
    display.print("s");

    display.display(); // 更新显示
  }
}
